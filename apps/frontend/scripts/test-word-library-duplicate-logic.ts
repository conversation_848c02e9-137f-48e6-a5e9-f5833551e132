/**
 * 词库查重逻辑测试脚本
 * 🎯 测试目标：验证同一词库内重复检测和跨词库重复高亮功能
 * 📦 测试范围：格式验证、重复检测、高亮颜色分配
 */

import { 
  validateWord, 
  createInitialWordLibraryState,
  updateDuplicateWordsMapWithColors,
  createWordEntry,
  createEmptyWordLibrary
} from '../core/wordLibrary/WordLibraryCore';
import { createWordLibraryKey } from '../core/matrix/MatrixTypes';
import type { WordLibraryState, WordLibraryKey } from '../core/matrix/MatrixTypes';

// 测试用例
interface TestCase {
  name: string;
  description: string;
  test: () => void;
}

// 创建测试状态
function createTestState(): WordLibraryState {
  const state = createInitialWordLibraryState();
  
  // 添加一些测试数据
  const blackLibrary = state.libraries.get('black-1');
  const redLibrary = state.libraries.get('red-1');
  
  if (blackLibrary) {
    // 黑1词库已经有"主题"这个词
    blackLibrary.words.push(createWordEntry('测试', 'black', 1));
    blackLibrary.words.push(createWordEntry('验证', 'black', 1));
  }
  
  if (redLibrary) {
    // 红1词库添加一些词语，包括与黑1重复的
    redLibrary.words.push(createWordEntry('主题', 'red', 1)); // 与黑1重复
    redLibrary.words.push(createWordEntry('开发', 'red', 1));
  }
  
  return state;
}

// 测试用例定义
const testCases: TestCase[] = [
  {
    name: '同一词库内重复检测',
    description: '在黑1词库中添加已存在的"主题"，应该被拒绝',
    test: () => {
      const state = createTestState();
      const result = validateWord('主题', 'black-1', state.libraries);
      
      console.log('测试结果:', result);
      
      if (!result.isValid && result.errors.includes('词语"主题"在当前词库中已存在')) {
        console.log('✅ 同一词库内重复检测正确');
      } else {
        console.log('❌ 同一词库内重复检测失败');
      }
    }
  },
  
  {
    name: '格式验证测试',
    description: '测试各种格式错误的词语',
    test: () => {
      const state = createTestState();
      
      // 测试太短的词语
      const shortResult = validateWord('短', 'black-1', state.libraries);
      console.log('短词语测试:', shortResult);
      
      // 测试太长的词语
      const longResult = validateWord('这是一个很长的词语', 'black-1', state.libraries);
      console.log('长词语测试:', longResult);
      
      // 测试包含标点的词语
      const punctResult = validateWord('测试，', 'black-1', state.libraries);
      console.log('标点词语测试:', punctResult);
      
      // 测试非中文词语
      const englishResult = validateWord('test', 'black-1', state.libraries);
      console.log('英文词语测试:', englishResult);
      
      if (!shortResult.isValid && !longResult.isValid && !punctResult.isValid && !englishResult.isValid) {
        console.log('✅ 格式验证正确');
      } else {
        console.log('❌ 格式验证失败');
      }
    }
  },
  
  {
    name: '跨词库重复检测',
    description: '在蓝1词库中添加与其他词库重复的词语，应该允许但标记为重复',
    test: () => {
      const state = createTestState();
      const result = validateWord('主题', 'blue-1', state.libraries);
      
      console.log('跨词库重复测试结果:', result);
      
      if (result.isValid && result.isDuplicate && result.duplicateLibraries.length > 0) {
        console.log('✅ 跨词库重复检测正确');
        console.log('重复的词库:', result.duplicateLibraries);
      } else {
        console.log('❌ 跨词库重复检测失败');
      }
    }
  },
  
  {
    name: '高亮颜色分配测试',
    description: '测试重复词语的高亮颜色分配',
    test: () => {
      const state = createTestState();
      
      // 使用增强版函数更新重复词语映射和高亮颜色
      const result = updateDuplicateWordsMapWithColors(
        state.libraries,
        state.wordHighlightColors,
        state.usedHighlightColors
      );
      
      console.log('重复词语映射:', Array.from(result.duplicateWords.entries()));
      console.log('高亮颜色映射:', Array.from(result.wordHighlightColors.entries()));
      console.log('已使用颜色:', Array.from(result.usedHighlightColors));
      
      // 检查"主题"是否被正确标记为重复并分配了颜色
      const hasThemeInDuplicates = result.duplicateWords.has('主题');
      const hasThemeColor = result.wordHighlightColors.has('主题');
      
      if (hasThemeInDuplicates && hasThemeColor) {
        console.log('✅ 高亮颜色分配正确');
      } else {
        console.log('❌ 高亮颜色分配失败');
      }
    }
  },
  
  {
    name: '正常词语添加测试',
    description: '添加格式正确且不重复的词语，应该成功',
    test: () => {
      const state = createTestState();
      const result = validateWord('新词语', 'black-1', state.libraries);
      
      console.log('正常词语添加测试结果:', result);
      
      if (result.isValid && !result.isDuplicate) {
        console.log('✅ 正常词语添加正确');
      } else {
        console.log('❌ 正常词语添加失败');
      }
    }
  }
];

// 运行测试
function runTests() {
  console.log('🚀 开始词库查重逻辑测试\n');
  
  testCases.forEach((testCase, index) => {
    console.log(`\n📋 测试 ${index + 1}: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    console.log('---');
    
    try {
      testCase.test();
    } catch (error) {
      console.log('❌ 测试执行失败:', error);
    }
    
    console.log('---');
  });
  
  console.log('\n✨ 测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests();
}

export { runTests };
